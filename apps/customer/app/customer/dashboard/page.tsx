'use client'

import React, { Suspense } from 'react'
import DashboardClient from './client'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@ui/components/ui/card'
import { truncate } from '@utils/text-utils'
import { Badge } from '@ui/components/ui/badge'
import { CherryIcon, FlagIcon, Leaf, MessageCircleXIcon, UnlinkIcon } from 'lucide-react'
import { createSegments } from '@/components/issues'
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@ui/components/ui/tabs'
import { Summarize } from '@/components/summarize'
import Donut from '@ui/components/graph/donut/donut'
import { cn } from '@utils/lib/utils'
import { Citation } from '@/components/citation'

import { getRatingText, ScoreCard } from '@/components/charts/score-cards'
import { Timeline } from '@/components/timeline/timeline'
import { CherryTypeV2, FlagTypeV2, ModelSectionType, PromiseTypeV2, VagueType } from '@/types'
import { processFlags } from '@/utils/flag-converter'
import { ScoreTypeV2 } from '@/types/score'
import Link from 'next/link'
import { PromisesListV2 } from '@/app/customer/dashboard/gw/promises/promises-list'
import { ClaimsListV2 } from '@/app/customer/dashboard/gw/claims/claims-list-v2'

import { SkeletonDashboard } from '@/components/skeletons/skeleton-dashboard'
import { Gauge } from '@/components/charts/gauge'
import { useAuth } from '@/components/context/auth/auth-context'
import { useEntity } from '@/components/context/entity/entity-context'
import { Headline } from '@ui/components/front-page/headline'
import { ClaimTypeV2 } from '@/types/claim'

const variants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 },
}


function FlagBadge(props: {
  flag: FlagTypeV2,
  callbackfn: (data: any, j: number) => React.JSX.Element,
  className: string,
  admin: boolean

}) {
  const { queryString } = useEntity()
  return <Link href={'/customer/dashboard/flags/' + props.flag.id + '?' + queryString} passHref><Badge
    variant="secondary"
    className={cn(props.className, 'm-')}>
    {props.flag.model.flag_short_title || props.flag.issue || 'Untitled Flag'}
  </Badge></Link>
}

function RedFlagBadges({ flags, admin }: { flags: FlagTypeV2[], admin: boolean }) {
  const [isExpanded, setIsExpanded] = React.useState(false)
  const sortedFlags = flags.sort((a, b) => (b.model.impact || 0) - (a.model.impact || 0))
  const displayFlags = isExpanded ? sortedFlags : sortedFlags.slice(0, 30)
  const hasMore = flags.length > 30

  return (
    <div data-testid="red-flags-expanded-list">
      {displayFlags.map((flag, i) => (
        <FlagBadge key={i} flag={flag} className="bg-red-200 dark:bg-red-950 m-1"
                   admin={admin}
                   callbackfn={(data, j) => (
                     <Citation key={j} data={data} admin={admin} />
                   )} />
      ))}
      {hasMore && (
        <Badge
          variant="secondary"
          className="cursor-pointer hover:bg-muted/80 transition-colors"
          onClick={() => setIsExpanded(!isExpanded)}
          data-testid={isExpanded ? "red-flags-show-less" : "red-flags-show-more"}
        >
          {isExpanded ? 'Show less' : `+${flags.length - 30} more`}
        </Badge>
      )}
    </div>
  )
}

function GreenFlagBadges({ flags, admin }: { flags: FlagTypeV2[], admin: boolean }) {
  const [isExpanded, setIsExpanded] = React.useState(false)
  const sortedFlags = flags.sort((a, b) => (b.model.impact || 0) - (a.model.impact || 0))
  const displayFlags = isExpanded ? sortedFlags : sortedFlags.slice(0, 30)
  const hasMore = flags.length > 30

  return (
    <div data-testid="green-flags-expanded-list">
      {displayFlags.map((flag, i) => (
        <FlagBadge key={i} flag={flag} className="bg-green-200 dark:bg-green-950 mr-1"
                   admin={admin}
                   callbackfn={(data, j) => (
                     <Citation key={j} data={data} admin={admin} />
                   )} />
      ))}
      {hasMore && (
        <Badge
          variant="secondary"
          className="cursor-pointer hover:bg-muted/80 transition-colors"
          onClick={() => setIsExpanded(!isExpanded)}
          data-testid={isExpanded ? "green-flags-show-less" : "green-flags-show-more"}
        >
          {isExpanded ? 'Show less' : `+${flags.length - 30} more`}
        </Badge>
      )}
    </div>
  )
}

// Legacy functions for backward compatibility - now with badge functionality
function redFlagBadges(redFlags: FlagTypeV2[], admin: boolean) {
  const [isExpanded, setIsExpanded] = React.useState(false)
  
  return (
    <div>
      <Badge 
        variant="secondary" 
        className="cursor-pointer hover:bg-muted/80 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
        data-testid="red-flags-badge"
      >
        Red Flags ({redFlags.length})
      </Badge>
      {isExpanded && <RedFlagBadges flags={redFlags} admin={admin} />}
    </div>
  )
}

function greenFlagBadges(greenFlags: FlagTypeV2[], admin: boolean) {
  const [isExpanded, setIsExpanded] = React.useState(false)
  
  return (
    <div>
      <Badge 
        variant="secondary" 
        className="cursor-pointer hover:bg-muted/80 transition-colors"
        onClick={() => setIsExpanded(!isExpanded)}
        data-testid="green-flags-badge"
      >
        Green Flags ({greenFlags.length})
      </Badge>
      {isExpanded && <GreenFlagBadges flags={greenFlags} admin={admin} />}
    </div>
  )
}

function GreenwashingSummary({
                               cherryData,
                               promisesData,
                               claimsData,
                               flagsData,
                               modelSectionsData,
                               vagueData,
                               vagueDetailData,
                               entityData,
                               model,
                               admin,
                               score,
                               scoreData,
                             }: {
  cherryData: CherryTypeV2[];
  promisesData: PromiseTypeV2[];
  claimsData: ClaimTypeV2[];
  flagsData: FlagTypeV2[];
  modelSectionsData: ModelSectionType[];
  vagueData: VagueType | null;
  vagueDetailData: VagueType[];
  entityData: any;
  model: string;
  admin: boolean,
  score: number | null,
  scoreData?: ScoreTypeV2
}) {
  // Use disclosure filter toggle directly from entity context
  const entityContext = useEntity()
  const includeDisclosures = entityContext.includeDisclosures

  console.log('GW Summary')
  const dbscanCherry = cherryData && cherryData.length > 0 ? cherryData.filter((item) => item.label === 'dbscan') : []
  const cherryPickingCount = cherryData ? cherryData.length : 0
  const invalidClaims = claimsData && claimsData.length > 0 ? claimsData.filter((item) => !item.verified) : []
  const brokenPromises = promisesData && promisesData.length > 0 ? promisesData.filter((item) => !item.kept) : []
  const invalidClaimsCount = invalidClaims ? invalidClaims.length : 0

  // Process flags to ensure models are properly parsed
  // First convert to unknown, then to FlagTypeV2[] to avoid type errors
  const processedFlags = processFlags(flagsData as unknown as FlagTypeV2[])

  // Filter flags based on disclosure setting
  const filteredFlags = processedFlags.filter(flag =>
    includeDisclosures || (flag.model && !flag.model.is_disclosure_only || flag.model.flag_type === 'red'),
  )

  // Filter flags based on type (disclosure filtering already applied to filteredFlags)
  const filterFlags = (flags: FlagTypeV2[], type: string) => {
    return flags
      .filter(flag => flag.model.flag_type === type)
  }

  const greenFlags = filteredFlags && filteredFlags.length > 0 ? filterFlags(filteredFlags, 'green') : []
  const redFlags = filteredFlags && filteredFlags.length > 0 ? filterFlags(filteredFlags, 'red') : []

  // Count disclosure-only flags
  const disclosureOnlyCount = flagsData.filter(flag => flag.model.is_disclosure_only).length
  const hasDisclosureFlags = disclosureOnlyCount > 0
  const redFlagsForSummary = redFlags && redFlags.length > 0 ? redFlags.map((item) => ({
    'id': item.id,
    'type': item.model.flag_type + '-flag',
    'issue': item.model.flag_title || item.issue || 'Untitled Flag',
    'reason': truncate(item.model.flag_summary || '', 1000),
  })) : []
  const greenFlagsForSummary = greenFlags && greenFlags.length > 0 ? greenFlags.map((item) => ({
    'id': item.id,
    'type': item.model.flag_type + '-flag',
    'issue': item.model.flag_title || item.issue || 'Untitled Flag',
    'reason': truncate(item.model.flag_summary || '', 1000),
  })) : []
  const cherryDataForSummary = cherryData && cherryData.length > 0 ? cherryData.map((item) => ({
    'id': item.id,
    'type': 'cherry',
    'reason': item.model.reason,
    'explanation': item.model.explanation,
    'score': item.model.score,
  })) : []
  const invalidClaimsForSummary = invalidClaims && invalidClaims.length > 0 ? invalidClaims.map((item) => ({
    'id': item.id,
    'type': 'claim',
    'summary': item.model.summary,
    'valid_claim': item.model.valid_claim,
    'greenwashing': item.model.greenwashing,
    'verdict_confidence': item.model.confidence,
  })) : []

  function overallRatingText() {
    // First, prioritize the rating from xfer_score_v2 if available
    if (scoreData && scoreData.model && scoreData.model.rating_text) {
      return scoreData.model.rating_text
    }

    // Check if we have no data
    const vagueTermsCount = vagueDetailData && vagueDetailData.length ? vagueDetailData.length : 0
    const promisesCount = promisesData && promisesData.length ? promisesData.length : 0

    if (greenFlagCount === 0 && redFlagCount === 0 && cherryPickingCount === 0 &&
      invalidClaimsCount === 0 && vagueTermsCount === 0 && promisesCount === 0) {
      return 'No Data'
    }

    // Perfect score case
    if (redFlagCount == 0 && overallRating == 100) {
      return 'Perfect'
    }

    // Fallback to calculated rating
    return getRatingText(overallRating)
  }

  function severityText() {
    if (scoreData && scoreData.model && scoreData.model.minor_major_text) {
      return scoreData.model.minor_major_text
    }
    return ''
  }

  // Create a compatible format for createSegments
  const compatibleFlags = filteredFlags

  let {
    socialSegmentsRed,
    socialSegmentsGreen,
    ecoSegmentsRed,
    ecoSegmentsGreen,
    governanceSegmentsGreen,
    governanceSegmentsRed,
    ethicalModelSegments,
  } = createSegments(model, modelSectionsData, compatibleFlags)

  const flagToRatingMapper = (row: FlagTypeV2, i: number): number =>
    (row.model.confidence || 0) * ((row.model.impact || 0) / 100)

  const greenFlagRating = greenFlags.map(flagToRatingMapper).reduce((a, b) => a + b, 0)
  const redFlagRating = redFlags.map(flagToRatingMapper).reduce((a, b) => a + b, 0)
  const greenFlagAvgRating = greenFlags.length ? greenFlagRating / greenFlags.length : 0
  const greenFlagCount = greenFlags.length ? greenFlags.length : 0
  const redFlagCount = redFlags.length ? redFlags.length : 0
  const redFlagAvgRating = redFlags.length ? redFlagRating / redFlags.length : 0
  const flagRating = redFlagRating / (greenFlagRating + redFlagRating)
  const flagAvgRating = redFlagAvgRating / (greenFlagAvgRating + redFlagAvgRating)

  // Use the score directly from the scoreData if available
  let overallRating = scoreData && scoreData.model && scoreData.model.score !== undefined ? scoreData.model.score : null
  console.log('Overall Rating', overallRating, score)

  // Additional metrics from scoreData
  const redFlagsCount = scoreData && scoreData.model ? scoreData.model.red_flags_count : redFlagCount
  const greenFlagsCount = scoreData && scoreData.model ? scoreData.model.green_flags_count : greenFlagCount
  const redFlagsScore = scoreData && scoreData.model ? scoreData.model.red_flags_score : redFlagRating
  const greenFlagsScore = scoreData && scoreData.model ? scoreData.model.green_flags_score : greenFlagRating
  const averageRed = scoreData && scoreData.model ? scoreData.model.average_red : redFlagAvgRating
  const averageGreen = scoreData && scoreData.model ? scoreData.model.average_green : greenFlagAvgRating


  return (
    <div className="mx-4 space-y-6" data-testid="dashboard-content">
      {/* Client component to reset navigation path */}
      <DashboardClient />

      <Headline className="text-center mt-4">{entityData?.name}</Headline>
      <div className="space-y-4 mt-2 md:ml-6 md:mr-6">
        <Summarize hashId={'gw-overall-summary-mobile-' + entityContext.hash()} className="lg:hidden"
                   preamble="Please keep the summary to maximum of 100 words, remain balanced and impartial you are providing an analysis for ekoIntelligence."
                   obj={{
                     companyDescription: entityData?.description,
                     overallRating: overallRatingText(),
                     red: redFlagsForSummary,
                     green: greenFlagsForSummary,
                   }} />

        {/* Disclosure toggle is now in the EMR navigation */}

        <div
          className="grid gap-6 grid-cols-1 md:grid-cols-[repeat(2,_minmax(180px,1fr))] xl:grid-cols-[repeat(2,_minmax(180px,1fr))] w-full  2xl:grid-cols-[repeat(2,_minmax(200px,1fr))] grid-rows-1">
          <Card className={cn(
            'col-span-1 md:col-span-1  lg:col-span-2 xl:col-span-1 2xl:col-span-1 rounded-2xl shadow-medium overflow-hidden',
            overallRating && overallRating >= 70 ? 'glass-effect-brand-strong-lit' :
              overallRating && overallRating >= 40 ? 'glass-effect-brand-alt-strong-lit' :
                'glass-effect-brand-compliment-strong-lit',
          )}>
            <div className={cn(
              'absolute top-0 left-0 right-0 h-1',
              overallRating && overallRating >= 70 ? 'bg-brand-gradient' :
                overallRating && overallRating >= 40 ? 'bg-brand-gradient-accent' :
                  'bg-brand-gradient-compliment-strong',
            )}></div>
            <CardHeader>
              <CardTitle>
                <div className="grid gap-4 grid-cols-1 lg:grid-cols-2">
                  <div className="w-full text-center heading-4">Overall Rating</div>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 grid-cols-1 xl:grid-cols-2">
                <div>
                  <Gauge
                    value={overallRating}
                    size="xl"
                    showValue={true}
                    color={overallRating && overallRating >= 70 ? 'text-brand' :
                      overallRating && overallRating >= 40 ? 'text-brand-accent' :
                        'text-brand-compliment'}
                    label={overallRatingText()}
                    data-testid="overall-rating-gauge"
                  />
                  {/*{severityText() &&*/}
                  {/*  <div className="text-center text-sm mt-2 font-medium">{severityText()}</div>}*/}
                </div>
                <div className="items-start justify-center hidden lg:flex">
                  This is the overall indicative rating we have assigned for {entityData.name}. The
                  rating is a combination of the flags we have identified, as well as cherry-picking,
                  false claims and broken promises.
                </div>
              </div>
            </CardContent>
          </Card>

          {/*<GuageCard*/}
          {/*  score={redFlags ? 100 - Math.round(flagRating * 100) : -1}*/}
          {/*  label="Negative Ratio"*/}
          {/*  labelTooltip={'The ratio of bad actions to all actions, weighted by confidence and impact.'}*/}
          {/*  subtext="Total Negative/All Weighted Ratio"*/}
          {/*  inverse={false}*/}
          {/*  color={true}*/}
          {/*  percentage={true}*/}
          {/*  scoreTooltip={'The ratio of bad actions to all actions, weighted by confidence and impact.'}*/}
          {/*  scoreForColor={null} />*/}


          {/*<GuageCard*/}
          {/*  score={100 - Math.round(flagAvgRating * 100)}*/}
          {/*  label="Negative Action Ratio (Avg)"*/}
          {/*  labelTooltip={'The average ratio of bad actions to all actions, weighted by confidence and impact.'}*/}
          {/*  subtext="Average Negative/All Weighted Ratio"*/}
          {/*  color={true}*/}
          {/*  scoreForColor={null}*/}
          {/*  percentage={true}*/}
          {/*  scoreTooltip={'The average ratio of bad actions to all actions,\n weighted by confidence and impact.'}*/}
          {/*  inverse={false} />*/}

          <div
            className="grid gap-4 space-4 grid-cols-2 sm:grid-cols-3  md:grid-cols-2 lg:grid-cols-1 xl:grid-cols-2 2xl:grid-cols-2">
            <ScoreCard href={'/customer/dashboard/flags' + '?' + entityContext.queryString + '&flag-type=green'}
                       score={greenFlags.length}
                       label="Green&nbsp;Flags"
                       labelTooltip={'Positive actions relating to ESG activities that have been flagged.'}
                       icon={Leaf}
                       subtext={` ${Math.round(greenFlagRating * 100) / 100}`}
                       subTextTooltip={'The total impact rating for all positive actions flagged, higher is better.'}
                       scoreTooltip={'The total positive actions that have been flagged.'}
                       color={true}
                       inverse={false}
                       scoreForColor={greenFlagRating ? 100 : 0}
                       data-testid="green-flags-card"
            />

            <ScoreCard href={'/customer/dashboard/flags' + '?' + entityContext.queryString + '&flag-type=red'}
                       score={redFlags.length}
                       label="Red&nbsp;Flags"
                       labelTooltip={'Negative actions relating to ESG activities that have been flagged.'}
                       icon={FlagIcon}
                       subtext={`${Math.round(redFlagRating * 100) / 100}`}
                       subTextTooltip={'The total impact rating for all negative actions flagged, higher is worse.'}
                       scoreTooltip={'The total negative actions that have been flagged.'}
                       inverse={true}
                       color={true}
                       scoreForColor={redFlagRating ? 100 : 0}
                       data-testid="red-flags-card"
            />
            <ScoreCard href={'/customer/dashboard/flags' + '?' + entityContext.queryString}
                       score={averageRed}
                       label="Average Red"
                       labelTooltip={'Negative actions relating to ESG activities that have been flagged.'}
                       icon={FlagIcon}
                       subtext="Lower is better"
                       percentage={true}
                       subTextTooltip={'The average impact rating for all negative actions flagged, higher is worse.'}
                       scoreTooltip={'The average impact rating for all negative actions that have been flagged.'}
                       inverse={false}
                       color={true}
                       scoreForColor={averageRed < 20 ? 100 : averageRed > 40 ? 0 : 100 - averageRed}
            />

            <ScoreCard href={'/customer/dashboard/gw/cherry' + '?' + entityContext.queryString}
                       score={cherryPickingCount}
                       label="Cherry-Picking"
                       labelTooltip={'The act of selecting only the best data to support a claim.'}
                       icon={CherryIcon}
                       inverse={false}
                       color={false}
                       scoreTooltip={'The total number of cherry-picking instances identified.'}
                       scoreForColor={cherryPickingCount * 4}
                       data-testid="cherry-picking-card"
            />

            <ScoreCard href={'/customer/dashboard/gw/claims' + '?' + entityContext.queryString}
                       score={claimsData && claimsData.length ? Math.round(100 * invalidClaimsCount / claimsData.length) : 0}
                       label="False Claims"
                       labelTooltip={'Claims made by an entity that are found to be false.'}
                       icon={MessageCircleXIcon}
                       inverse={true}
                       color={true}
                       percentage={true}
                       scoreForColor={claimsData && claimsData.length ? Math.round(100 * invalidClaimsCount / claimsData.length) : 0}
                       scoreTooltip={'The total number of false claims identified.'}
                       subtext={`${invalidClaimsCount || 0}/${claimsData && claimsData.length ? claimsData.length : 0}`}
                       subTextTooltip={'Percentage of ESG related claims made which were found to be false.'}
                       data-testid="false-claims-card"
            />


            {/*<ScoreCard href={'/customer/dashboard/gw/vague' + '?' + entityContext.queryString}*/}
            {/*           score={vagueDetailData && vagueDetailData.length ? vagueDetailData.length : 0}*/}
            {/*           label="Vague Terms"*/}
            {/*           labelTooltip={'Terms used by an entity that are vague or unclear.'}*/}
            {/*           icon={MessageSquareDashedIcon}*/}
            {/*           inverse={true}*/}
            {/*           color={true}*/}
            {/*           scoreForColor={(vagueDetailData && vagueDetailData.length ? vagueDetailData.length : 0) * 5}*/}
            {/*           scoreTooltip={'The total number of vague terms identified.'}*/}
            {/*/>*/}

            <ScoreCard href={'/customer/dashboard/gw/promises'}
                       score={promisesData && promisesData.length && brokenPromises ? Math.round(brokenPromises.length / promisesData.length * 100) : 0}
                       label="Broken&nbsp;Promises"
                       labelTooltip={'Promises made by an entity that have not been kept.'}
                       icon={UnlinkIcon}
                       inverse={true}
                       color={true}
                       percentage={true}
                       scoreForColor={promisesData && promisesData.length && brokenPromises ? Math.round(brokenPromises.length / promisesData.length * 100) : 0}
                       subtext={`${brokenPromises ? brokenPromises.length : 0}/${promisesData ? promisesData.length : 0}`}
                       scoreTooltip={'The % of promises that have been broken.'}
                       data-testid="broken-promises-card"
            />

            {/*<ScoreCard href={'/customer/dashboard/prediction-v2' + '?' + entityContext.queryString}*/}
            {/*           score={100}*/}
            {/*           label="Prediction&nbsp;V2"*/}
            {/*           labelTooltip={'Advanced future trend predictions with hierarchical analysis.'}*/}
            {/*           icon={LineChartIcon}*/}
            {/*           inverse={false}*/}
            {/*           color={true}*/}
            {/*           percentage={false}*/}
            {/*           scoreForColor={100}*/}
            {/*           subtext="Advanced Analysis"*/}
            {/*           scoreTooltip={'View detailed hierarchical prediction analysis for this entity.'}*/}
            {/*/>*/}
          </div>
        </div>

        <Tabs defaultValue="summary" className="hidden lg:block w-full max-w-[100dvw]" data-testid="dashboard-tabs">
          <TabsList className="glass-effect-subtle rounded-xl p-1 space-x-1">
            <TabsTrigger value="summary"
                         className="glass-effect data-[state=active]:glass-effect-brand-strong-lit rounded-lg transition-all duration-300"
                         data-testid="tab-summary">Summary</TabsTrigger>
            {redFlagCount > 0 && <TabsTrigger value="red"
                                              className="glass-effect data-[state=active]:glass-effect-brand-strong-lit rounded-lg transition-all duration-300"
                                              data-testid="tab-red-flags">Red
              Flags</TabsTrigger>}
            {greenFlagCount > 0 && <TabsTrigger value="green"
                                                className="glass-effect data-[state=active]:glass-effect-brand-strong-lit rounded-lg transition-all duration-300"
                                                data-testid="tab-green-flags">Green
              Flags</TabsTrigger>}
            {promisesData && promisesData.length > 0 &&
              <TabsTrigger value="promises"
                           className="glass-effect data-[state=active]:glass-effect-brand-strong-lit rounded-lg transition-all duration-300"
                           data-testid="tab-promises">Promises</TabsTrigger>}
            {vagueDetailData && vagueDetailData.length > 0 &&
              <TabsTrigger value="vague"
                           className="glass-effect data-[state=active]:glass-effect-brand-strong-lit rounded-lg transition-all duration-300">Vague
                Terms</TabsTrigger>}
            {cherryPickingCount > 0 && <TabsTrigger value="cherry"
                                                    className="glass-effect data-[state=active]:glass-effect-brand-strong-lit rounded-lg transition-all duration-300"
                                                    data-testid="tab-cherry-picking">Cherry
              Picking</TabsTrigger>}
            {invalidClaimsCount > 0 && <TabsTrigger value="claims"
                                                    className="glass-effect data-[state=active]:glass-effect-brand-strong-lit rounded-lg transition-all duration-300"
                                                    data-testid="tab-claims">Claims</TabsTrigger>}
          </TabsList>
          <TabsContent value="summary" data-testid="summary-content">
            <Card className="glass-effect-strong-lit rounded-2xl">
              <CardContent className="space-y-4 text-lg min-h-[400px]">

                <Summarize
                  className="prose mx-auto"
                  preamble="Please keep the summary to maximum of 200 words without headings, remain balanced and impartial you are providing an analysis for ekoIntelligence."
                  keepCitations={false}
                  obj={{
                    companyDescription: entityData?.description,
                    overallRating: overallRatingText(),
                    red: redFlagsForSummary,
                    green: greenFlagsForSummary,
                    cherry: cherryDataForSummary,
                    claims: invalidClaimsForSummary,
                    promises: promisesData,
                    vague: vagueData,
                  }} hashId={'gw-overall-summary-' + entityContext.hash()} />


              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="red" data-testid="red-flags-content">
            {/* Red Flags Section */}
            <Card>
              <CardHeader>
                <CardTitle>Red Flags Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4  min-h-[600px]">
                <Suspense>
                  <Summarize hashId={'gw-red-flags-summary-' + entityContext.hash()}
                             keepCitations={false}

                             preamble="Please keep the summary to maximum of 200 words without headings."
                             obj={redFlagsForSummary} />
                </Suspense>
                <div className="flex flex-wrap gap-2">
                  {redFlagBadges(redFlags, admin)}
                </div>
                {/*<h1 className="text-xl bold">Flags</h1>*/}
                {/*<ScrollArea className="h-64" scrollHideDelay={0}>*/}
                {/*    {redFlags.map((item) => (*/}
                {/*        <FlagSummary key={item.id} item={item} issueMap={issueMap}/>*/}
                {/*    ))}*/}
                {/*</ScrollArea>*/}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="green" data-testid="green-flags-content">

            <Card>
              <CardHeader>
                <CardTitle>Green Flags Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4 min-h-[600px]">
                <Suspense>
                  <Summarize hashId={'v2-gw-green-flags-summary-' + entityContext.hash()}
                             preamble="Please keep the summary to maximum 200 words"
                             obj={greenFlagsForSummary} />
                </Suspense>
                <div className="flex flex-wrap gap-2">
                  {greenFlagBadges(greenFlags, admin)}
                </div>
                {/*<h1 className="text-xl bold">Flags</h1>*/}
                {/*<ScrollArea className="h-64" scrollHideDelay={0}>*/}
                {/*    {greenFlags.map((item) => (*/}
                {/*        <FlagSummary key={item.id} item={item} issueMap={issueMap}/>*/}
                {/*    ))}*/}
                {/*</ScrollArea>*/}
              </CardContent>
            </Card>

          </TabsContent>
          <TabsContent value="promises" data-testid="promises-content">
            <Card>
              <CardHeader>
                <CardTitle>Broken Promises Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4  min-h-[600px]">
                  <Suspense>
                    <Summarize hashId={'gw-promises-summary-' + entityContext.hash()}
                               preamble="Please keep the summary to maximum of 200 words"
                               obj={promisesData} />
                  </Suspense>

                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/*<TabsContent value="vague">*/}
          {/*  <Card>*/}
          {/*    <CardHeader>*/}
          {/*      <CardTitle>Vague Terms Summary</CardTitle>*/}
          {/*    </CardHeader>*/}
          {/*    <CardContent className="  min-h-[300px]">*/}
          {/*      {invalidClaimsForSummary ?*/}
          {/*        <Summarize hashId={'gw-vague-summary-' + entityContext.hash()}*/}
          {/*                   preamble="Keep the summary to maximum 200 words"*/}
          {/*                   obj={{*/}
          {/*                     claims: invalidClaimsForSummary,*/}
          {/*                     summary: vagueData && vagueData.model ? vagueData.model.summary : '',*/}
          {/*                   }} />*/}
          {/*        : <div>No data</div>*/}
          {/*      }*/}

          {/*    </CardContent>*/}
          {/*  </Card>*/}

          {/*</TabsContent>*/}

          <TabsContent value="cherry" data-testid="cherry-picking-content">
            <Card>
              <CardHeader>
                <CardTitle>Cherry Picking Summary</CardTitle>
              </CardHeader>
              <CardContent className=" min-h-[600px]">
                <Suspense>
                  <Summarize hashId={'gw-cherry-summary-' + entityContext.hash()}
                             preamble="Keep the summary to max 200 words"
                             obj={cherryDataForSummary} />
                </Suspense>


              </CardContent>
            </Card>
          </TabsContent>
          <TabsContent value="claims" data-testid="claims-content">
            <Card>
              <CardHeader>
                <CardTitle>Invalid Claims Summary</CardTitle>
              </CardHeader>
              <CardContent className="  min-h-[300px]">
                <div className="space-y-4">
                  <Summarize hashId={'gw-invalid-claims-summary-' + entityContext.hash()}
                             preamble="Please keep the summary to max 200 words"
                             obj={invalidClaimsForSummary} />
                </div>

              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        <div className="grid gap-4   grid-cols-1  lg:grid-cols-1 xl:grid-cols-2">
          {/* Disclosure toggle is now in the EMR navigation */}

          <Card className="glass-effect-brand-lit rounded-3xl overflow-hidden">
            <div className="absolute top-0 left-0 right-0 h-1 bg-brand-gradient"></div>
            <CardHeader className="flex items-center justify-between">
              <CardTitle className="heading-5 text-center w-full">Positive Actions</CardTitle>
            </CardHeader>
            <CardContent className="text-center mx-auto">
              <Donut
                className="mx-auto"
                id={'doughnut-green'}
                breakpoints={{
                  0: 300,
                  400: 300,
                  600: 500,
                  768: 400,
                  1024: 600,
                  1280: 400,
                  1536: 500,
                }}
                ecoSegments={ecoSegmentsGreen}
                socialSegments={socialSegmentsGreen}
                governanceSegments={governanceSegmentsGreen}
                colorScale={'green-flags' as any}
                model={ethicalModelSegments}
                data-testid="positive-actions-chart"
              />
              <div className="w-full mt-8">{greenFlagBadges(greenFlags, admin)}</div>
            </CardContent>
          </Card>

          <Card className="glass-effect-brand-compliment-lit rounded-3xl overflow-hidden">
            <div className="absolute top-0 left-0 right-0 h-1 bg-brand-gradient-compliment"></div>
            <CardHeader className="flex items-center justify-between">
              <CardTitle className="heading-5 text-center w-full">Negative Actions</CardTitle>
            </CardHeader>
            <CardContent className="grid text-center mx-auto">
              <Donut
                className="mx-auto"
                id={'doughnut-red'}
                breakpoints={{
                  0: 300,
                  400: 300,
                  600: 500,
                  768: 400,
                  1024: 600,
                  1280: 400,
                  1536: 500,
                }}
                ecoSegments={ecoSegmentsRed}
                socialSegments={socialSegmentsRed}
                governanceSegments={governanceSegmentsRed}
                colorScale={'red-flags' as any}
                model={ethicalModelSegments}
                data-testid="negative-actions-chart"
              />
              <div className="w-full mt-8">{redFlagBadges(redFlags, admin)}</div>
            </CardContent>
          </Card>


        </div>
        <div className="grid gap-4 grid-cols-1 lg:grid-cols-1 xl:grid-cols-2 ">

          <Card className="col-span-2 glass-effect-lit rounded-3xl overflow-hidden ">
            <div className="absolute top-0 left-0 right-0 h-1 bg-blue-400"></div>
            <CardHeader>
              <CardTitle className="heading-5">Timeline</CardTitle>
              <CardDescription className="text-foreground/70">Timeline of events</CardDescription>
            </CardHeader>
            <CardContent className="mx-auto overflow-hidden">
              <Timeline preamble="" variant="compact" entity={entityData}
                        obj={{
                          red: redFlagsForSummary,
                          green: greenFlagsForSummary,
                          claims: invalidClaimsForSummary,
                          promises: promisesData,
                        }}
                        data-testid="dashboard-timeline" />
            </CardContent>
          </Card>
          <Card className="col-span-2 glass-effect-lit rounded-3xl overflow-hidden">
            <div className="absolute top-0 left-0 right-0 h-1 bg-brand-gradient-compliment"></div>
            <CardHeader className="pb-3">
              <CardTitle className="heading-5 flex items-center justify-between">
                <span>Example False Claims</span>
                <Link
                  href={'/customer/dashboard/gw/claims' + '?' + entityContext.queryString}
                  className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-normal"
                >
                  more →
                </Link>
              </CardTitle>
              <CardDescription className="text-foreground/70">
                {entityData.name && `Examples of false claims made by ${entityData.name}`}
              </CardDescription>
            </CardHeader>
            <CardContent>
              {/* Convert V1 claims to V2 format for ClaimsListV2 */}
              {invalidClaims.length > 0 ? (
                <div className="max-h-[800px] overflow-y-auto">
                  <ClaimsListV2
                    claimsData={invalidClaims!}
                    admin={admin}
                  />
                </div>
              ) : (
                <p className="text-sm text-foreground/70 text-center py-4">
                  No false claims found
                </p>
              )}
            </CardContent>
          </Card>

          <Card className="col-span-2 glass-effect-lit rounded-3xl shadow-medium border-border/20 overflow-hidden">
            <div className="absolute top-0 left-0 right-0 h-1 bg-brand-gradient"></div>
            <CardHeader className="pb-3">
              <CardTitle className="heading-5 flex items-center justify-between">
                <span>Example Promises</span>
                <Link
                  href={'/customer/dashboard/gw/promises' + '?' + entityContext.queryString}
                  className="text-sm text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 font-normal"
                >
                  more →
                </Link>
              </CardTitle>
              <CardDescription className="text-foreground/70">
                {entityData.name && `Examples of promises made by ${entityData.name}`}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <PromisesListV2 promisesData={promisesData} admin={admin} />
            </CardContent>
          </Card>

          {/*<Card className="col-span-2">*/}
          {/*    <CardHeader className="pb-3">*/}
          {/*        <CardTitle className="text-xl font-semibold">Vague Terms</CardTitle>*/}
          {/*        <CardDescription>*/}
          {/*            {entityData.name && `A list of vague terms used by ${entityData.name}`}*/}
          {/*        </CardDescription>*/}
          {/*    </CardHeader>*/}
          {/*    <CardContent>*/}
          {/*        <div className="space-y-4">*/}
          {/*            <VagueTermsList vagueDetailData={vagueDetailData} admin={admin}/>*/}
          {/*        </div>*/}
          {/*    </CardContent>*/}
          {/*</Card>*/}

        </div>
      </div>

      {/*<div className="space-y-4 ml-4 mr-4  md:ml-6 md:mr-6 lg:hidden">*/}
      {/*    <Summarize hashId="gw-expanded-summary-mobile"*/}
      {/*               preamble="Please keep the summary to about 400 words with headings, remain balanced and impartial you are providing an analysis for ekoIntelligence."*/}
      {/*               obj={{*/}
      {/*                   overallRating: getRatingText(overallRating),*/}
      {/*                   red: redFlagsForSummary,*/}
      {/*                   green: greenFlagsForSummary,*/}
      {/*                   cherry: cherryDataForSummary,*/}
      {/*                   claims: invalidClaimsForSummary,*/}
      {/*                   promises: promisesData,*/}
      {/*                   vague: vagueData*/}
      {/*               }}/>*/}
      {/*</div>*/}


    </div>

  )

}


async function runAsync(f: () => {}) {
  await f()
}

export default function Page() {
  const auth = useAuth()
  const entityContext = useEntity()

  // Show loading state if any data is still loading
  if (entityContext.isLoading()) {
    return <SkeletonDashboard />
  }

  // Show loading state if required data is not available
  if (!entityContext.entity || !entityContext.model || !entityContext.runObject ||
    !entityContext.entityData) {
    return <SkeletonDashboard />
  }


  return (
    <Suspense fallback={<SkeletonDashboard />}>
      <GreenwashingSummary
        cherryData={entityContext.cherryData || []}
        promisesData={entityContext.promisesData || []}
        claimsData={entityContext.claimsData || []}
        vagueData={entityContext.vagueData || null}
        vagueDetailData={entityContext.vagueDetailData || []}
        flagsData={entityContext.flagsData || []}
        modelSectionsData={entityContext.modelSectionsData || []}
        entityData={entityContext.entityData}
        model={entityContext.model}
        score={entityContext.score}
        scoreData={entityContext.scoreData || undefined}
        admin={auth.admin}
      />
    </Suspense>
  )
}
